image: maven:3.9.6-eclipse-temurin-21

stages:
  - build
  - unit-test
  - acceptance-test
  - package
  - docker-build-push

cache:
  paths:
    - .m2/repository

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end"
  DOCKER_DRIVER: overlay2
  GIT_COMMIT_SHORT: "$CI_COMMIT_SHORT_SHA"
  VERSION: "0.3.0"
  REGISTRY_IMAGE: "$CI_REGISTRY_IMAGE/robot-worlds-server"

before_script:
  - apt-get update && apt-get install -y make lsof curl gnupg2 ca-certificates
  - echo "Java version:"
  - java -version
  - echo "Maven version:"
  - mvn -version
  - git config --global user.name "CI Bot"
  - git config --global user.email "<EMAIL>"

build-project:
  stage: build
  script:
    - cd scripts
    - make clean
    - make build
  only:
    - main
    - merge_requests

unit-test:
  stage: unit-test
  script:
    - cd scripts
    - make unit-test
  only:
    - main
    - merge_requests

acceptance-test:
  stage: acceptance-test
  script:
    - cd scripts
    - make acceptance-test
  needs:
    - build-project
  only:
    - main
    - merge_requests

package-project:
  stage: package
  script:
    - cd scripts
    - make package
  artifacts:
    paths:
      - target/robot-world-*-jar-with-dependencies.jar
    expire_in: 1 hour
  only:
    - main
    - merge_requests

docker-build-push:
  image: docker:25.0.3
  stage: docker-build-push
  services:
    - docker:25.0.3-dind
  before_script: []
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login "$CI_REGISTRY" -u "$CI_REGISTRY_USER" --password-stdin
    - docker build -t "$REGISTRY_IMAGE:$GIT_COMMIT_SHORT" .
    - docker tag "$REGISTRY_IMAGE:$GIT_COMMIT_SHORT" "$REGISTRY_IMAGE:latest"
    - docker push "$REGISTRY_IMAGE:latest"
  needs:
    - package-project
  only:
    - main
