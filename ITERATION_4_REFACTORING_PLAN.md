# Iteration 4: Layered Architecture Refactoring Plan

## Overview
This document outlines the detailed tasks required to refactor the Robot World codebase to comply with proper layered architecture principles, addressing all four sections of Iteration 4 requirements.

## Section 1: Audit & Refactor Thread, Console, and Domain Code for Architecture Compliance

### 1.1 Current Architecture Violations Identified

#### Threading & Infrastructure Classes (Currently Mixed with Domain)
- **Server.java**: Contains threading, console I/O, networking, and domain logic
- **ClientHandler.java**: Threading mixed with domain access
- **World.java**: Domain class with console output (`System.out.println`)
- **ConfigLoader.java**: Infrastructure with console output

#### Domain Layer Violations
- **World.displayWorld()**: Contains `System.out.println` - presentation concern in domain
- **ClientHandler.run()**: Direct `world.displayWorld()` calls from infrastructure

#### Persistence Layer Violations
- **WorldDAOImpl**: Direct domain object creation and manipulation
- **Server**: Direct DAO usage and persistence logic mixing

### 1.2 Detailed Refactoring Tasks

#### Task 1.2.1: Create Presentation Layer Structure
```
src/main/java/za/co/wethinkcode/robots/presentation/
├── controllers/
│   ├── NetworkController.java      # TCP connection handling
│   ├── RobotController.java        # Robot command processing
│   └── AdminController.java        # Admin console commands
├── dto/
│   ├── RobotCommandRequest.java    # Request DTOs
│   └── RobotStateResponse.java     # Response DTOs
└── display/
    └── WorldDisplayService.java    # World visualization
```

#### Task 1.2.2: Create Service Layer Structure
```
src/main/java/za/co/wethinkcode/robots/service/
├── GameService.java               # Main game orchestration
├── WorldService.java              # World management
├── RobotService.java              # Robot operations
└── CommandExecutionService.java   # Command processing
```

#### Task 1.2.3: Create Infrastructure Layer Structure
```
src/main/java/za/co/wethinkcode/robots/infrastructure/
├── networking/
│   ├── ServerManager.java         # Server lifecycle
│   └── ConnectionManager.java     # Connection pooling
├── persistence/
│   ├── data/                      # Data objects
│   ├── dao/                       # Enhanced DAO layer
│   └── mappers/                   # Domain-Data mapping
└── configuration/
    └── ApplicationContext.java    # Dependency injection
```

#### Task 1.2.4: Refactor Domain Layer
- **Remove infrastructure concerns from World.java**
  - Extract `displayWorld()` to `WorldDisplayService`
  - Remove `System.out.println` calls
  - Keep only pure domain logic

- **Clean up Command classes**
  - Ensure no I/O or persistence code
  - Keep only command logic and validation

#### Task 1.2.5: Extract Threading Logic
- **Create ServerManager.java**
  - Move thread creation and management from Server.java
  - Handle client connection threading
  - Manage admin console thread

- **Create ConnectionManager.java**
  - Extract socket handling from Server.java
  - Manage client connection lifecycle
  - Handle network I/O operations

#### Task 1.2.6: Extract Console Logic
- **Create AdminConsoleService.java**
  - Move admin console logic from Server.java
  - Handle user input/output
  - Manage console session lifecycle

- **Create WorldDisplayService.java**
  - Extract display logic from World.java
  - Handle world visualization
  - Manage console output formatting

## Section 2: Implement HTTP Client Requests in Python & Java

### 2.1 Python Implementation Tasks

#### Task 2.1.1: Create Python HTTP Client
- **File**: `src/main/python/cat_fact_client.py`
- **Requirements**:
  - Use `http.client.HTTPSConnection`
  - Connect to `catfact.ninja`
  - Send GET request to `/fact`
  - Include `Accept: application/json` header
  - Print HTTP status code and response body

#### Task 2.1.2: Python Implementation Details
```python
# Pseudo-code structure:
import http.client
import json

def fetch_cat_fact():
    # Create HTTPS connection
    # Send GET request with headers
    # Read response
    # Print status and decoded JSON
    # Handle errors gracefully
```

### 2.2 Java Implementation Tasks

#### Task 2.2.1: Add Unirest Dependency
- **File**: `pom.xml`
- **Add dependency**:
```xml
<dependency>
    <groupId>com.konghq</groupId>
    <artifactId>unirest-java</artifactId>
    <version>3.14.5</version>
</dependency>
```

#### Task 2.2.2: Create Java HTTP Client
- **File**: `src/main/java/za/co/wethinkcode/robots/examples/RandomCatFact.java`
- **Requirements**:
  - Use Unirest HTTP client
  - Send GET request to `https://catfact.ninja/fact`
  - Print HTTP status code and JSON response body
  - Handle exceptions properly

## Section 3: Implement Web API Layer for RobotWorld

### 3.1 Web Framework Setup Tasks

#### Task 3.1.1: Add Javalin Dependency
- **File**: `pom.xml`
- **Add dependencies**:
```xml
<dependency>
    <groupId>io.javalin</groupId>
    <artifactId>javalin</artifactId>
    <version>5.6.3</version>
</dependency>
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.15.2</version>
</dependency>
```

#### Task 3.1.2: Create API Server Structure
```
src/main/java/za/co/wethinkcode/robots/api/
├── ApiServer.java                 # Main API server class
├── controllers/
│   ├── WorldApiController.java    # World endpoints
│   └── RobotApiController.java    # Robot endpoints
├── dto/
│   ├── WorldResponse.java         # World API responses
│   ├── RobotCommandRequest.java   # Robot command requests
│   └── ApiErrorResponse.java      # Error responses
└── config/
    └── ApiConfiguration.java      # API configuration
```

### 3.2 API Endpoint Implementation Tasks

#### Task 3.2.1: Implement GET /world Endpoint
- **Controller**: `WorldApiController.java`
- **Method**: `getCurrentWorld()`
- **Response**: Current world state as JSON
- **Requirements**:
  - Return world dimensions, obstacles, robots
  - Use proper JSON serialization
  - Handle errors gracefully

#### Task 3.2.2: Implement GET /world/{name} Endpoint
- **Controller**: `WorldApiController.java`
- **Method**: `getWorldByName(String name)`
- **Response**: Restored world from database as JSON
- **Requirements**:
  - Restore world from database via service layer
  - Return 404 if world not found
  - Return world state as JSON

#### Task 3.2.3: Implement POST /robot/{name} Endpoint
- **Controller**: `RobotApiController.java`
- **Method**: `executeRobotCommand(String robotName, RobotCommandRequest request)`
- **Response**: Command execution result as JSON
- **Requirements**:
  - Support launch command initially
  - Validate request format
  - Execute command via service layer
  - Return standardized response format

### 3.3 API Testing Tasks

#### Task 3.3.1: Create Unit Tests
- **Files**:
  - `WorldApiControllerTest.java`
  - `RobotApiControllerTest.java`
  - `ApiServerTest.java`
- **Requirements**:
  - Test all endpoints
  - Verify JSON serialization
  - Test error handling
  - Mock service layer dependencies

#### Task 3.3.2: Create Integration Tests
- **File**: `ApiIntegrationTest.java`
- **Requirements**:
  - Test full API workflow
  - Use test database
  - Verify end-to-end functionality

## Section 4: Refactor RobotWorld Persistence to Use ORM/Data Access Interface

### 4.1 Data Object Design Tasks

#### Task 4.1.1: Create Data Objects
```
src/main/java/za/co/wethinkcode/robots/infrastructure/persistence/data/
├── WorldData.java                 # World persistence data
├── RobotData.java                 # Robot persistence data
├── ObstacleData.java              # Obstacle persistence data
└── GameSessionData.java           # Game session data
```

#### Task 4.1.2: Design Data Object Structure
- **WorldData.java**:
  - id, name, width, height, created_at, updated_at
  - No domain logic, pure data container

- **RobotData.java**:
  - id, world_id, name, make, x, y, direction, shields, shots, status
  - Foreign key relationship to WorldData

- **ObstacleData.java**:
  - id, world_id, type, x, y, width, height
  - Foreign key relationship to WorldData

### 4.2 Data Access Interface Tasks

#### Task 4.2.1: Create DAI Interfaces
```
src/main/java/za/co/wethinkcode/robots/infrastructure/persistence/dao/
├── WorldDataAccess.java           # World data operations
├── RobotDataAccess.java           # Robot data operations
├── ObstacleDataAccess.java        # Obstacle data operations
└── GameSessionDataAccess.java     # Session data operations
```

#### Task 4.2.2: Implement SQL Annotations
- **Use MyBatis or similar for SQL annotations**
- **Example structure**:
```java
public interface WorldDataAccess {
    @Select("SELECT * FROM worlds WHERE name = #{name}")
    WorldData findByName(String name);
    
    @Insert("INSERT INTO worlds (name, width, height) VALUES (#{name}, #{width}, #{height})")
    void save(WorldData worldData);
    
    @Update("UPDATE worlds SET width = #{width}, height = #{height} WHERE name = #{name}")
    void update(WorldData worldData);
    
    @Delete("DELETE FROM worlds WHERE name = #{name}")
    void delete(String name);
}
```

### 4.3 Mapping Layer Tasks

#### Task 4.3.1: Create Mapper Classes
```
src/main/java/za/co/wethinkcode/robots/infrastructure/persistence/mappers/
├── WorldMapper.java               # World ↔ WorldData mapping
├── RobotMapper.java               # Robot ↔ RobotData mapping
└── ObstacleMapper.java            # Obstacle ↔ ObstacleData mapping
```

#### Task 4.3.2: Implement Mapping Logic
- **WorldMapper.java**:
  - `toData(World world)` → `WorldData`
  - `toDomain(WorldData data)` → `World`
  - Handle null values and validation

### 4.4 Database Schema Updates

#### Task 4.4.1: Update Database Schema
- **Add robots table**:
```sql
CREATE TABLE robots (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    world_name TEXT NOT NULL,
    name TEXT NOT NULL,
    make TEXT NOT NULL,
    x INTEGER NOT NULL,
    y INTEGER NOT NULL,
    direction TEXT NOT NULL,
    shields INTEGER NOT NULL,
    shots INTEGER NOT NULL,
    status TEXT NOT NULL,
    FOREIGN KEY (world_name) REFERENCES worlds(name) ON DELETE CASCADE,
    UNIQUE(world_name, name)
);
```

#### Task 4.4.2: Create Migration Scripts
- **File**: `src/main/resources/db/migration/`
- **Scripts for schema updates**
- **Data migration if needed**

### 4.5 Testing Tasks

#### Task 4.5.1: Create DAI Unit Tests
- **Use H2 in-memory database for testing**
- **Test all CRUD operations**
- **Verify SQL annotations work correctly**

#### Task 4.5.2: Create Mapper Tests
- **Test domain ↔ data object conversion**
- **Verify data integrity**
- **Test edge cases and null handling**

## Implementation Order

1. **Phase 1**: Section 1 - Architecture refactoring (Foundation)
2. **Phase 2**: Section 4 - ORM/DAI implementation (Data layer)
3. **Phase 3**: Section 2 - HTTP client examples (External integration)
4. **Phase 4**: Section 3 - Web API layer (New interface)

## Success Criteria

- [ ] All threading code moved to infrastructure layer
- [ ] Domain classes contain only domain logic
- [ ] Persistence layer uses proper ORM/DAI pattern
- [ ] Web API provides HTTP interface to robot world
- [ ] All tests pass with new architecture
- [ ] No cross-layer dependencies remain
- [ ] Code follows single responsibility principle

## Detailed File-by-File Refactoring Tasks

### Files to Create (New Architecture)

#### Presentation Layer
1. **NetworkController.java** - Extract socket handling from Server.java
2. **RobotController.java** - Extract command processing from Server.java
3. **AdminController.java** - Extract admin console from Server.java
4. **WorldDisplayService.java** - Extract display logic from World.java

#### Service Layer
5. **GameService.java** - Main game orchestration service
6. **WorldService.java** - World management operations
7. **RobotService.java** - Robot-specific operations
8. **CommandExecutionService.java** - Command processing logic

#### Infrastructure Layer
9. **ServerManager.java** - Server lifecycle and threading
10. **ConnectionManager.java** - Connection pooling and management
11. **ApplicationContext.java** - Dependency injection container

#### Data Access Layer
12. **WorldDataAccess.java** - World data operations interface
13. **RobotDataAccess.java** - Robot data operations interface
14. **WorldMapper.java** - Domain-Data mapping for worlds
15. **RobotMapper.java** - Domain-Data mapping for robots
16. **WorldData.java** - World data object
17. **RobotData.java** - Robot data object

#### API Layer
18. **ApiServer.java** - Main API server
19. **WorldApiController.java** - World REST endpoints
20. **RobotApiController.java** - Robot REST endpoints

#### Examples
21. **RandomCatFact.java** - Java HTTP client example
22. **cat_fact_client.py** - Python HTTP client example

### Files to Modify (Existing Code)

#### Domain Layer Cleanup
1. **World.java** - Remove System.out.println, keep only domain logic
2. **Robot.java** - Verify no infrastructure concerns (already clean)
3. **Command.java** - Verify no infrastructure concerns (already clean)

#### Infrastructure Updates
4. **Server.java** - Refactor to use new layered architecture
5. **ClientHandler.java** - Remove or refactor to use NetworkController
6. **DatabaseConnection.java** - Enhance for new DAI pattern
7. **WorldDAOImpl.java** - Replace with new DAI implementation

#### Configuration
8. **pom.xml** - Add new dependencies (Javalin, Unirest, MyBatis)

### Files to Remove (Deprecated)
1. **ClientHandler.java** - Functionality moved to NetworkController
2. **WorldDAO.java** - Replaced by new DAI interfaces
3. **WorldDAOImpl.java** - Replaced by new DAI implementations

## Risk Mitigation

### Breaking Changes
- Maintain backward compatibility during transition
- Create feature flags for new vs old architecture
- Implement gradual migration strategy

### Testing Strategy
- Create comprehensive test suite for new architecture
- Maintain existing tests during transition
- Add integration tests for layer boundaries

### Rollback Plan
- Keep original code in separate branch
- Document all changes for easy reversal
- Test rollback procedures

## Timeline Estimation

- **Phase 1 (Architecture)**: 3-4 days
- **Phase 2 (ORM/DAI)**: 2-3 days
- **Phase 3 (HTTP Clients)**: 1 day
- **Phase 4 (Web API)**: 2-3 days
- **Testing & Integration**: 2 days
- **Total**: 10-13 days
