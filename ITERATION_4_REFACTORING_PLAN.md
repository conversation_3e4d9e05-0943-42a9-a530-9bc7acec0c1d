# Iteration 4: Layered Architecture Refactoring Plan

## Overview
This document outlines the detailed tasks required to refactor the Robot World codebase to comply with proper layered architecture principles, addressing all four sections of Iteration 4 requirements.

## Section 1: Audit & Refactor Thread, Console, and Domain Code for Architecture Compliance

### 1.1 Current Architecture Violations Identified

#### Threading & Infrastructure Classes (Currently Mixed with Domain)
- **Server.java**: Contains threading, console I/O, networking, and domain logic
- **ClientHandler.java**: Threading mixed with domain access
- **World.java**: Domain class with console output (`System.out.println`)
- **ConfigLoader.java**: Infrastructure with console output

#### Domain Layer Violations
- **World.displayWorld()**: Contains `System.out.println` - presentation concern in domain
- **ClientHandler.run()**: Direct `world.displayWorld()` calls from infrastructure

#### Persistence Layer Violations
- **WorldDAOImpl**: Direct domain object creation and manipulation
- **Server**: Direct DAO usage and persistence logic mixing

### 1.2 Detailed Refactoring Tasks

#### Task 1.2.1: Create Presentation Layer Structure
```
src/main/java/za/co/wethinkcode/robots/presentation/
├── controllers/
│   ├── NetworkController.java      # TCP connection handling
│   ├── RobotController.java        # Robot command processing
│   └── AdminController.java        # Admin console commands
└── display/
    └── WorldDisplayService.java    # World visualization
```

**Note**: Using enhanced DAO pattern - controllers work directly with domain objects via service layer.

#### Task 1.2.2: Create Service Layer Structure
```
src/main/java/za/co/wethinkcode/robots/service/
├── GameService.java               # Main game orchestration
├── WorldService.java              # World management
├── RobotService.java              # Robot operations
└── CommandExecutionService.java   # Command processing
```

#### Task 1.2.3: Create Infrastructure Layer Structure
```
src/main/java/za/co/wethinkcode/robots/infrastructure/
├── networking/
│   ├── ServerManager.java         # Server lifecycle
│   └── ConnectionManager.java     # Connection pooling
├── persistence/
│   ├── data/                      # Data objects
│   ├── dao/                       # Enhanced DAO layer
│   └── mappers/                   # Domain-Data mapping
└── configuration/
    └── ApplicationContext.java    # Dependency injection
```

#### Task 1.2.4: Refactor Domain Layer
- **Remove infrastructure concerns from World.java**
  - Extract `displayWorld()` to `WorldDisplayService`
  - Remove `System.out.println` calls
  - Keep only pure domain logic

- **Clean up Command classes**
  - Ensure no I/O or persistence code
  - Keep only command logic and validation

#### Task 1.2.5: Extract Threading Logic
- **Create ServerManager.java**
  - Move thread creation and management from Server.java
  - Handle client connection threading
  - Manage admin console thread

- **Create ConnectionManager.java**
  - Extract socket handling from Server.java
  - Manage client connection lifecycle
  - Handle network I/O operations

#### Task 1.2.6: Extract Console Logic
- **Create AdminConsoleService.java**
  - Move admin console logic from Server.java
  - Handle user input/output
  - Manage console session lifecycle

- **Create WorldDisplayService.java**
  - Extract display logic from World.java
  - Handle world visualization
  - Manage console output formatting

## Section 2: Implement HTTP Client Requests in Python & Java

### 2.1 Python Implementation Tasks

#### Task 2.1.1: Create Python HTTP Client
- **File**: `src/main/python/cat_fact_client.py`
- **Requirements**:
  - Use `http.client.HTTPSConnection`
  - Connect to `catfact.ninja`
  - Send GET request to `/fact`
  - Include `Accept: application/json` header
  - Print HTTP status code and response body

#### Task 2.1.2: Python Implementation Details
```python
# Pseudo-code structure:
import http.client
import json

def fetch_cat_fact():
    # Create HTTPS connection
    # Send GET request with headers
    # Read response
    # Print status and decoded JSON
    # Handle errors gracefully
```

### 2.2 Java Implementation Tasks

#### Task 2.2.1: Add Unirest Dependency
- **File**: `pom.xml`
- **Add dependency**:
```xml
<dependency>
    <groupId>com.konghq</groupId>
    <artifactId>unirest-java</artifactId>
    <version>3.14.5</version>
</dependency>
```

#### Task 2.2.2: Create Java HTTP Client
- **File**: `src/main/java/za/co/wethinkcode/robots/examples/RandomCatFact.java`
- **Requirements**:
  - Use Unirest HTTP client
  - Send GET request to `https://catfact.ninja/fact`
  - Print HTTP status code and JSON response body
  - Handle exceptions properly

## Section 3: Implement Web API Layer for RobotWorld

### 3.1 Web Framework Setup Tasks

#### Task 3.1.1: Add Javalin Dependency
- **File**: `pom.xml`
- **Add dependencies**:
```xml
<dependency>
    <groupId>io.javalin</groupId>
    <artifactId>javalin</artifactId>
    <version>5.6.3</version>
</dependency>
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.15.2</version>
</dependency>
```

#### Task 3.1.2: Create API Server Structure
```
src/main/java/za/co/wethinkcode/robots/api/
├── ApiServer.java                 # Main API server class
├── controllers/
│   ├── WorldApiController.java    # World endpoints
│   └── RobotApiController.java    # Robot endpoints
└── config/
    └── ApiConfiguration.java      # API configuration
```

**Note**: Using DAO pattern instead of DTOs - API controllers will work directly with domain objects and DAO responses.

### 3.2 API Endpoint Implementation Tasks

#### Task 3.2.1: Implement GET /world Endpoint
- **Controller**: `WorldApiController.java`
- **Method**: `getCurrentWorld()`
- **Response**: Current world state as JSON
- **Requirements**:
  - Return world dimensions, obstacles, robots
  - Use proper JSON serialization
  - Handle errors gracefully

#### Task 3.2.2: Implement GET /world/{name} Endpoint
- **Controller**: `WorldApiController.java`
- **Method**: `getWorldByName(String name)`
- **Response**: Restored world from database as JSON
- **Requirements**:
  - Restore world from database via service layer
  - Return 404 if world not found
  - Return world state as JSON

#### Task 3.2.3: Implement POST /robot/{name} Endpoint
- **Controller**: `RobotApiController.java`
- **Method**: `executeRobotCommand(String robotName, RobotCommandRequest request)`
- **Response**: Command execution result as JSON
- **Requirements**:
  - Support launch command initially
  - Validate request format
  - Execute command via service layer
  - Return standardized response format

### 3.3 API Testing Tasks

#### Task 3.3.1: Create Unit Tests
- **Files**:
  - `WorldApiControllerTest.java`
  - `RobotApiControllerTest.java`
  - `ApiServerTest.java`
- **Requirements**:
  - Test all endpoints
  - Verify JSON serialization
  - Test error handling
  - Mock service layer dependencies

#### Task 3.3.2: Create Integration Tests
- **File**: `ApiIntegrationTest.java`
- **Requirements**:
  - Test full API workflow
  - Use test database
  - Verify end-to-end functionality

## Section 4: Refactor RobotWorld Persistence to Use Enhanced DAO Pattern

### 4.1 Enhanced DAO Pattern Tasks

**Note**: Using enhanced DAO pattern instead of ORM to maintain consistency with existing codebase architecture.

#### Task 4.1.1: Create Enhanced DAO Interfaces
```
src/main/java/za/co/wethinkcode/robots/dao/
├── WorldDAO.java                  # Enhanced world operations
├── RobotDAO.java                  # Robot persistence operations
├── ObstacleDAO.java               # Obstacle operations (optional)
└── impl/
    ├── WorldDAOImpl.java          # Enhanced implementation
    ├── RobotDAOImpl.java          # Robot DAO implementation
    └── ObstacleDAOImpl.java       # Obstacle DAO implementation
```

#### Task 4.1.2: Design Enhanced DAO Structure
- **RobotDAO.java**:
  - saveRobot(), findRobotByName(), findAllRobots()
  - updateRobot(), deleteRobot(), robotExists()
  - Works directly with Robot domain objects

- **Enhanced WorldDAO.java**:
  - Existing methods plus saveWorldMetadata(), loadObstacles()
  - getAllWorldNames(), deleteWorld()
  - Better separation of world vs robot persistence

- **Database Schema Updates**:
  - Add robots table with foreign key to worlds
  - Maintain existing worlds and obstacles tables

### 4.2 Enhanced DAO Implementation Tasks

#### Task 4.2.1: Implement RobotDAO
- **RobotDAOImpl.java**:
  - Direct JDBC implementation (consistent with existing pattern)
  - Methods work with Robot domain objects
  - Handle robot persistence in robots table

#### Task 4.2.2: Enhance WorldDAO
- **Enhanced WorldDAOImpl.java**:
  - Add methods for better separation of concerns
  - saveWorldMetadata(), loadObstacles(), getAllWorldNames()
  - Maintain existing functionality

#### Task 4.2.3: Database Operations
- **Use existing JDBC pattern** (consistent with current WorldDAOImpl)
- **Example RobotDAO methods**:
```java
public interface RobotDAO {
    void saveRobot(String worldName, Robot robot) throws SQLException;
    Robot findRobotByName(String worldName, String robotName) throws SQLException;
    List<Robot> findAllRobots(String worldName) throws SQLException;
    void updateRobot(String worldName, Robot robot) throws SQLException;
    void deleteRobot(String worldName, String robotName) throws SQLException;
    boolean robotExists(String worldName, String robotName) throws SQLException;
}
```

### 4.3 Service Layer Integration Tasks

#### Task 4.3.1: Create Service Layer with DAO Integration
- **GameService.java**:
  - Inject WorldDAO and RobotDAO
  - Orchestrate business operations
  - Handle world and robot persistence

#### Task 4.3.2: Implement Service Methods
- **GameService methods**:
  - `executeCommand()` - execute and persist robot state changes
  - `saveCurrentWorld()` - save world and all robots
  - `restoreWorld()` - restore world and robots from database
  - `removeRobot()` - remove from world and database

### 4.4 Database Schema Updates

#### Task 4.4.1: Update DatabaseConnection.java
- **Add robots table creation**:
```sql
CREATE TABLE IF NOT EXISTS robots (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    world_name TEXT NOT NULL,
    name TEXT NOT NULL,
    make TEXT NOT NULL,
    x INTEGER NOT NULL,
    y INTEGER NOT NULL,
    direction TEXT NOT NULL,
    shields INTEGER NOT NULL,
    shots INTEGER NOT NULL,
    status TEXT NOT NULL,
    FOREIGN KEY (world_name) REFERENCES worlds(name) ON DELETE CASCADE,
    UNIQUE(world_name, name)
);
```

#### Task 4.4.2: Update initializeDatabase() Method
- **Add robots table creation to existing method**
- **Maintain existing worlds and obstacles tables**
- **Ensure foreign key constraints are enabled**

### 4.5 Testing Tasks

#### Task 4.5.1: Create Enhanced DAO Unit Tests
- **RobotDAOImplTest.java**:
  - Test all robot CRUD operations
  - Use existing test database pattern
  - Verify robot persistence and retrieval

#### Task 4.5.2: Create Service Layer Tests
- **GameServiceTest.java**:
  - Test service orchestration with mocked DAOs
  - Verify business logic execution
  - Test error handling and rollback scenarios

#### Task 4.5.3: Update Existing Tests
- **Enhance WorldDAOImplTest.java**:
  - Test new enhanced methods
  - Verify robot-world relationships
  - Test cascade delete operations

## Implementation Order

1. **Phase 1**: Section 1 - Architecture refactoring (Foundation)
2. **Phase 2**: Section 4 - ORM/DAI implementation (Data layer)
3. **Phase 3**: Section 2 - HTTP client examples (External integration)
4. **Phase 4**: Section 3 - Web API layer (New interface)

## Success Criteria

- [ ] All threading code moved to infrastructure layer
- [ ] Domain classes contain only domain logic
- [ ] Persistence layer uses proper ORM/DAI pattern
- [ ] Web API provides HTTP interface to robot world
- [ ] All tests pass with new architecture
- [ ] No cross-layer dependencies remain
- [ ] Code follows single responsibility principle

## Detailed File-by-File Refactoring Tasks

### Files to Create (New Architecture)

#### Presentation Layer
1. **NetworkController.java** - Extract socket handling from Server.java
2. **RobotController.java** - Extract command processing from Server.java
3. **AdminController.java** - Extract admin console from Server.java
4. **WorldDisplayService.java** - Extract display logic from World.java

#### Service Layer
5. **GameService.java** - Main game orchestration service with DAO integration
6. **WorldService.java** - World management operations (optional)
7. **RobotService.java** - Robot-specific operations (optional)

#### Infrastructure Layer
8. **ServerManager.java** - Server lifecycle and threading
9. **ConnectionManager.java** - Connection pooling and management
10. **ApplicationContext.java** - Dependency injection container

#### Enhanced DAO Layer
11. **RobotDAO.java** - Robot data operations interface
12. **RobotDAOImpl.java** - Robot DAO implementation
13. **Enhanced WorldDAO.java** - Extended world operations
14. **Enhanced WorldDAOImpl.java** - Enhanced implementation

#### API Layer
15. **ApiServer.java** - Main API server
16. **WorldApiController.java** - World REST endpoints (uses DAO via services)
17. **RobotApiController.java** - Robot REST endpoints (uses DAO via services)

#### Examples
18. **RandomCatFact.java** - Java HTTP client example
19. **cat_fact_client.py** - Python HTTP client example

### Files to Modify (Existing Code)

#### Domain Layer Cleanup
1. **World.java** - Remove System.out.println, keep only domain logic
2. **Robot.java** - Verify no infrastructure concerns (already clean)
3. **Command.java** - Verify no infrastructure concerns (already clean)

#### Infrastructure Updates
4. **Server.java** - Refactor to use new layered architecture
5. **ClientHandler.java** - Remove or refactor to use NetworkController
6. **DatabaseConnection.java** - Enhance for new DAI pattern
7. **WorldDAOImpl.java** - Replace with new DAI implementation

#### Configuration
8. **pom.xml** - Add new dependencies (Javalin, Unirest, MyBatis)

### Files to Remove (Deprecated)
1. **ClientHandler.java** - Functionality moved to NetworkController

### Files to Keep and Enhance
1. **WorldDAO.java** - Enhance with additional methods
2. **WorldDAOImpl.java** - Enhance existing implementation
3. **DatabaseConnection.java** - Add robots table creation

## Risk Mitigation

### Breaking Changes
- Maintain backward compatibility during transition
- Create feature flags for new vs old architecture
- Implement gradual migration strategy

### Testing Strategy
- Create comprehensive test suite for new architecture
- Maintain existing tests during transition
- Add integration tests for layer boundaries

### Rollback Plan
- Keep original code in separate branch
- Document all changes for easy reversal
- Test rollback procedures

## Timeline Estimation

- **Phase 1 (Architecture)**: 3-4 days
- **Phase 2 (ORM/DAI)**: 2-3 days
- **Phase 3 (HTTP Clients)**: 1 day
- **Phase 4 (Web API)**: 2-3 days
- **Testing & Integration**: 2 days
- **Total**: 10-13 days
