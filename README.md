# 🤖 Robot World Server

## 🌎 Project Overview 

Robot World is a multi-client server simulation where users launch robots into a shared alien world. The system uses a client-server architecture where multiple robot clients can connect to a central server and interact within the same environment.

### 🏗️ Architecture

- **Server**: Manages the world state, processes commands, and coordinates robot interactions
- **Client**: Connects to the server and sends commands for robot control
- **World**: Contains obstacles, manages robot positions, and enforces game rules
- **Robots**: Two types available - `tank` (high shields, low shots) and `sniper` (low shields, high shots)

### ✨ Features

- **Multi-client support**: Multiple robots can connect and interact simultaneously
- **Real-time interaction**: Robots can see, move, shoot, and affect each other
- **Obstacle system**: Dynamic obstacle placement (mountains, pits, etc.)
- **Robot types**: Tank and Sniper with different capabilities
- **Admin console**: Server-side monitoring and control
- **Docker support**: Containerized deployment with health checks

---

## 🚀 Getting Started

This project uses **Java 21** and **Maven** as the build tool.

### 📦 Dependencies  

- **[<PERSON>](https://github.com/FasterXML/jackson)** - JSON processing
- **[JSON (org.json)](https://mvnrepository.com/artifact/org.json/json)** - JSON utilities  
- **[PicoCLI](https://picocli.info/)** - Command line interface
- **[JUnit 5](https://junit.org/junit5/)** - Unit testing framework
- **[Mockito](https://mockito.org/)** - Mocking framework for tests
- **[Flow](https://github.com/wethinkcode/flow)** - Code quality tracking
- 

### 🛠 Prerequisites

- **Java 21** or higher
- **Maven 3.9.6** or higher
- **Docker** (optional, for containerized deployment)

### 📥 Installation

#### Installing Maven

##### 🍏 macOS (Using [Homebrew](https://brew.sh))

```bash
brew install maven
mvn -v  # Verify installation
```

##### 🐧 Ubuntu/Debian

```bash
sudo apt update && sudo apt install maven
mvn -v  # Verify installation
```

##### 🪟 Windows (Using [Scoop](https://scoop.sh))

```powershell
scoop install maven
mvn -v  # Verify installation
```

### 🏗 Building the Project

#### Using Maven

```bash
# Clone the repository
git clone <repository-url>
cd robot-world

# Clean and compile
mvn clean compile

# Run tests
mvn test

# Package (creates JAR with dependencies)
mvn clean package
```

#### Using Makefile (Recommended)

```bash
cd scripts

# Build project (skip tests)
make build

# Run unit tests only
make unit-test

# Run acceptance tests
make acceptance-test

# Build and package
make release

# Full build with all tests
make all
```

### 🚀 Running the Server

#### Method 1: Using Maven

```bash
mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.server.Server"
```

#### Method 2: Using JAR file

```bash
java -jar target/robot-world-*-jar-with-dependencies.jar
```

#### Method 3: Using Makefile

```bash
cd scripts
make docker-build
make docker-run
```

### 🎮 Running the Client

```bash
mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.client.ClientApp"
```

### 🌐 Server Configuration

The server accepts command-line arguments:

```bash
java -jar robot-world-server.jar [OPTIONS]

Options:
  -p, --port <PORT>        Server port (default: 5000)
  -s, --size <SIZE>        World size (default: 1)
  -o, --obstacles <TYPE>   Obstacle type: mountain, pit, none (default: mountain)
```

Examples:
```bash
# Run on port 3000 with 2x2 world
java -jar robot-world-server.jar -p 3000 -s 2

# Run with no obstacles
java -jar robot-world-server.jar -o none

# Run on port 5050 with pits
java -jar robot-world-server.jar -p 5050 -o pit
```

---

## 🐳 Docker Deployment

### Building Docker Image

```bash
cd scripts
make docker-build
```

### Running with Docker

```bash
# Run on default port 5050
make docker-run

# Run interactively
make docker-run-interactive

# Run with custom arguments
docker run -p 5050:5050 robot-worlds-server:0.2.0 -p 5050 -s 2 -o mountain
```

### Docker Testing

```bash
# Test Docker container
make docker-test

# Clean up containers
make docker-clean
```

---

## 🧪 Testing

### Unit Tests

```bash
# Run all unit tests
mvn test

# Using Makefile
make unit-test
```

### Acceptance Tests

```bash
# Test against reference server
make test-ref-server

# Test against our server
make test-our-server

# Full acceptance test suite
make acceptance-test
```

### Running Specific Tests

```bash
# Run specific test class
mvn test -Dtest="LaunchRobotTests"

# Run multiple test classes
mvn test -Dtest="LaunchRobotTests,RobotLookTests"
```

---

## 🔧 Troubleshooting

### Common Issues

**Port already in use:**
```bash
# Kill processes on port 5000
make kill-port-5000

# Kill processes on port 5050  
make kill-port-5050
```

**Maven build fails:**
```bash
# Clean and retry
mvn clean
mvn compile
```

**Docker issues:**
```bash
# Clean up Docker resources
make docker-clean

# Remove all containers and images
make docker-clean-all
```

### Getting Help

1. Check the [troubleshooting section](#-troubleshooting)
2. Review the [documentation](#-documentation)
3. Run tests to verify your setup: `make unit-test`
4. Check server logs for error messages

---

## 📄 License

This project is part of the WeThinkCode_ curriculum.

---

## 🌐 Network Setup

### Getting Local IP Address (Required for Client Connections)

**Note**: Run these commands on the machine hosting the server. Client and server must be on the same network.

#### 🍏 macOS
```bash
ipconfig getifaddr en0
# Output example: ************
```

#### 🪟 Windows
```cmd
for /f "tokens=14" %a in ('ipconfig ^| findstr "IPv4"') do @echo %a
# Output example: ************
```

#### 🐧 Linux
```bash
hostname -I
# Output example: ************ (first IP is usually your local IP)
```

### Client Connection

1. Start the server on the host machine
2. Note the IP address and port number
3. Run the client and enter:
   - **IP address**: The server's local IP (e.g., `************`)
   - **Port**: The server port (e.g., `5000` or `5050`)

---

## 🎮 Game Commands

Once connected, robots support these commands:

### Basic Commands
- `help` - Show available commands
- `state` - Get robot status and position
- `look` - See nearby objects and robots
- `orientation` - Get current facing direction

### Movement Commands
- `forward <steps>` - Move forward (1-5 steps)
- `back <steps>` - Move backward (1-3 steps)
- `turn left` - Turn 90° counterclockwise
- `turn right` - Turn 90° clockwise

### Combat Commands
- `fire` - Shoot in current direction
- `reload` - Reload ammunition
- `repair` - Repair shields

### Connection Commands
- `off` - Shutdown robot and disconnect
- `disconnect` - Disconnect from server

---

## 🤖 Robot Types

### Tank
- **Shields**: 10 (high durability)
- **Shots**: 3 (limited ammunition)
- **Range**: Variable (6 - current_shots)
- **Strategy**: Close combat, high survivability

### Sniper
- **Shields**: 5 (low durability)
- **Shots**: 20 (high ammunition)
- **Range**: Variable (current_shots - 9)
- **Strategy**: Long range, high firepower

---

## 🏗️ Build Scripts Reference

### Makefile Targets

```bash
# Development
make clean          # Clean build artifacts
make build          # Build without tests
make unit-test      # Run unit tests only
make acceptance-test # Run acceptance tests
make all           # Full build with all tests

# Versioning
make bump-patch     # Increment patch version
make bump-minor     # Increment minor version
make bump-major     # Increment major version

# Release
make release        # Create release JAR
make publish        # Release and push to Git
make publish-patch  # Bump patch, release, and publish

# Docker
make docker-build   # Build Docker image
make docker-run     # Run container on port 5050
make docker-test    # Test Docker container
make docker-clean   # Clean up containers
make docker-push    # Push to GitLab registry

# Utilities
make kill-port-5000 # Kill processes on port 5000
make kill-port-5050 # Kill processes on port 5050
```

---

## 🏷️ Version

Current version: **0.2.0-SNAPSHOT**

For version history and releases, see the [GitLab releases page](https://gitlab.wethinkco.de/nhmasiljhb024/brownfields-robot-worlds-jhb-01/-/releases).
