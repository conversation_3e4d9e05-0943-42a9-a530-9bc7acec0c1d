package za.co.wethinkcode.robots.api;

/**
 * TODO: Implement ApiServer (Section 3)
 * 
 * Responsibilities:
 * - Set up Javalin web server
 * - Configure REST API routes
 * - Handle HTTP requests/responses
 * - JSON serialization/deserialization
 * 
 * Dependencies needed in pom.xml:
 * - io.javalin:javalin
 * - com.fasterxml.jackson.core:jackson-databind
 * 
 * Routes to implement:
 * - GET /world -> current world state
 * - GET /world/{name} -> restore world from database
 * - POST /robot/{name} -> execute robot command
 */
public class ApiServer {
    // TODO: Implementation needed
}
