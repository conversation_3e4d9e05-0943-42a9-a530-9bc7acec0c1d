package za.co.wethinkcode.robots.examples;

/**
 * TODO: Implement RandomCatFact (Section 2)
 * 
 * Responsibilities:
 * - Demonstrate Java HTTP client using Unirest
 * - Send GET request to https://catfact.ninja/fact
 * - Print HTTP status code and response body
 * - Handle errors gracefully
 * 
 * Dependencies needed in pom.xml:
 * - com.konghq:unirest-java
 * 
 * Requirements:
 * - Use Unirest HTTP client library
 * - Print both status code and JSON response body
 * - Include proper error handling
 */
public class RandomCatFact {
    // TODO: Implementation needed
    
    public static void main(String[] args) {
        // TODO: Implement HTTP GET request to catfact.ninja/fact
    }
}
