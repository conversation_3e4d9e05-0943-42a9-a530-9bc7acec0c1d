package za.co.wethinkcode.robots.server;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import za.co.wethinkcode.flow.Recorder;
import za.co.wethinkcode.robots.server.db.DatabaseConnection;
import za.co.wethinkcode.robots.server.db.WorldDAO;
import za.co.wethinkcode.robots.server.db.WorldDAOImpl;

import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.sql.SQLException;
import java.util.Scanner;

/**
 * Main server class that accepts client connections and provides an admin console for server control.
 * Supports real-time robot monitoring, world state inspection, and graceful shutdown.
 */
public class Server {
    private static volatile boolean isRunning = true;
    private static ServerSocket serverSocket;
    private World world;
    private final WorldDAO worldDAO;
    private final ObjectMapper objectMapper;
    private final int portNumber;
    private final boolean testMode;

    public Server(int portNumber, int worldSize, String obstacleArg) {
        this(portNumber, worldSize, obstacleArg, false);
    }

    public Server(int portNumber, int worldSize, String obstacleArg, boolean testMode) {
        this.portNumber = portNumber;
        this.testMode = testMode;
        this.worldDAO = new WorldDAOImpl();
        this.objectMapper = new ObjectMapper();
        try {
            DatabaseConnection.initializeDatabase();
            this.world = createWorld(worldSize);
            if (obstacleArg != null && !obstacleArg.equalsIgnoreCase("none")) {
                processObstacleArgument(obstacleArg, world, ObstacleType.MOUNTAIN, 1);
            }
        } catch (SQLException e) {
            throw new RuntimeException("Failed to initialize database", e);
        }
    }


    public void start() {
        try {
            serverSocket = new ServerSocket(portNumber);
            System.out.println("Server started on port " + portNumber + ". Waiting for clients...");

            // Try to initialize flow tracking
            try {
                new Recorder().logRun();
            } catch (Exception e) {
                System.out.println("Flow tracking disabled: " + e.getMessage());
            }

            // Launch admin console thread only if not in test mode
            if (!testMode) {
                startAdminConsole();
            }
            world.displayWorld();

            while (isRunning) {
                Socket clientSocket = serverSocket.accept();
                System.out.println("New client connected: " + clientSocket.getRemoteSocketAddress());
                new Thread(() -> handleClient(clientSocket)).start();
            }
        } catch (IOException e) {
            if (!isRunning) {
                System.out.println("Server shutdown.");
            } else {
                System.out.println("Got an error: " + e);
            }
        }
    }

    private void handleClient(Socket clientSocket) {
        try (BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true)) {
            String request;
            while (isRunning && (request = in.readLine()) != null) {
                JsonNode response = handleCommand(request);
                out.println(objectMapper.writeValueAsString(response));
            }
        } catch (IOException e) {
            if (isRunning) e.printStackTrace();
        } finally {
            try {
                clientSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private JsonNode handleCommand(String request) {
        try {
            JsonNode jsonRequest = objectMapper.readTree(request);
            String command = jsonRequest.has("command") ? jsonRequest.get("command").asText().toLowerCase() : null;
            JsonNode jsonNodeResponse = null;
            if (command == null) {
                return createErrorResponse("Missing command field");
            }

            jsonNodeResponse = handleSaveCommand(command, jsonRequest);

            if (jsonNodeResponse == null) {
                jsonNodeResponse = handleRestoreCommand(command, jsonRequest);
            }

            if (jsonNodeResponse != null) {
                return jsonNodeResponse;
            }

            // Delegate all other commands (including save) to the Command framework
            try {
                org.json.JSONObject jsonObj = new org.json.JSONObject(request);
                za.co.wethinkcode.robots.commands.Command cmd = za.co.wethinkcode.robots.commands.Command.fromJSON(jsonObj);

                final JsonNode[] responseHolder = new JsonNode[1];
                world.execute(cmd, response -> {
                    try {
                        responseHolder[0] = objectMapper.readTree(response.toJSONString());
                    } catch (Exception e) {
                        responseHolder[0] = createErrorResponse("Failed to process response: " + e.getMessage());
                    }
                });

                return responseHolder[0] != null ? responseHolder[0] : createErrorResponse("No response received");
            } catch (IllegalArgumentException e) {
                return createErrorResponse("Unsupported command");
            }
        } catch (Exception e) {
            return createErrorResponse("Invalid request: " + e.getMessage());
        }
    }

    private JsonNode handleSaveCommand(String command, JsonNode jsonRequest) {
        if (!"save".equals(command)) {
            return null;
        }

        String saveWorldName = extractWorldName(jsonRequest);
        if (saveWorldName == null) {
            return createErrorResponse("Invalid arguments: Usage: save <worldName>");
        }

        JsonNode arguments = jsonRequest.get("arguments");
        return tryToSaveWorld(arguments, saveWorldName);
    }

    private JsonNode tryToSaveWorld(JsonNode arguments, String saveWorldName) {
        boolean forceOverwrite = isForceOverwrite(arguments);
        try {
            boolean worldExists = worldDAO.worldExists(saveWorldName);
            if (worldExists && !forceOverwrite) {
                return createErrorResponse("World '" + saveWorldName + "' already exists. Use: SAVE " + saveWorldName + " --force to overwrite.");
            }

            worldDAO.saveWorld(saveWorldName, world, forceOverwrite);
            return createSuccessResponse("World '" + saveWorldName + "' saved successfully");
        } catch (SQLException e) {
            return createErrorResponse("Failed to save world: " + e.getMessage());
        }
    }

    private static boolean isForceOverwrite(JsonNode arguments) {
        return arguments.size() == 2 && "--force".equalsIgnoreCase(arguments.get(1).asText());
    }

    private String extractWorldName(JsonNode jsonRequest) {
        JsonNode arguments = jsonRequest.get("arguments");
        if (!arguments.isArray() || arguments.size() < 1) {
            return null;
        }
        String name = arguments.get(0).asText();
        return (name == null || name.trim().isEmpty()) ? null : name;
    }

    private JsonNode handleRestoreCommand(String command, JsonNode jsonRequest) {
        if (!"restore".equals(command)) {
            return null;
        }

        JsonNode arguments = jsonRequest.get("arguments");
        if (!arguments.isArray() || arguments.size() != 1) {
            return createErrorResponse("Invalid arguments: Usage: restore <worldName>");
        }
        String restoreWorldName = arguments.get(0).asText();
        if (restoreWorldName == null || restoreWorldName.trim().isEmpty()) {
            return createErrorResponse("Invalid world name: Name cannot be empty");
        }
        try {
            restoreWorld(restoreWorldName.trim());
            return createSuccessResponse("World '" + restoreWorldName + "' restored successfully");
        } catch (SQLException e) {
            return createErrorResponse("Failed to restore world: " + e.getMessage());
        }
    }

    private synchronized void restoreWorld(String worldName) throws SQLException {
        this.world = worldDAO.restoreWorld(worldName);
    }

    private JsonNode createSuccessResponse(String message) {
        return objectMapper.createObjectNode()
                .put("result", "OK")
                .set("data", objectMapper.createObjectNode().put("message", message));
    }

    private JsonNode createErrorResponse(String message) {
        return objectMapper.createObjectNode()
                .put("result", "ERROR")
                .set("data", objectMapper.createObjectNode().put("message", message));
    }

    public static void main(String[] args) {
        int portNumber = 5000;
        int worldSize = 1;
        String obstacleArg = null;
        boolean testMode = false;
        isRunning = true;

        // Parse arguments
        ArgumentParseResult parsedArgs = parseArguments(args, new ServerConfig(portNumber, worldSize, testMode), obstacleArg);
        portNumber = parsedArgs.portNumber;
        worldSize = parsedArgs.worldSize;
        obstacleArg = parsedArgs.obstacleArg;
        testMode = parsedArgs.testMode;

        Server server = new Server(portNumber, worldSize, obstacleArg, testMode);
        server.start();
    }

    private static ArgumentParseResult parseArguments(String[] args, ServerConfig serverConfig, String obstacleArg) {
        for (int i = 0; i < args.length; i++) {
            if (i + 1 >= args.length) {
                continue;
            }

            switch (args[i]) {
                case "-p":
                    serverConfig.portNumber = Integer.parseInt(args[++i]);
                    break;
                case "-s":
                    serverConfig.worldSize = Integer.parseInt(args[++i]);
                    break;
                case "-o":
                    obstacleArg = args[++i];
                    break;
                case "-t":
                    serverConfig.testMode = true;
                    break;
                default:
                    System.out.println("Unknown argument: " + args[i]);
            }
        }
        return new ArgumentParseResult(serverConfig, obstacleArg);
    }

    private static World createWorld(int worldSize) {
        World world = new World(worldSize, worldSize);
        ConfigLoader configLoader = new ConfigLoader();
        configLoader.applyPropertiesToWorld(world, "config.properties");
        return world;
    }

    private static void processObstacleArgument(String obstacleArg, World world, ObstacleType obstacleType, int obstacleSize) {
        String[] coords = obstacleArg.split(",");
        if (coords.length == 2) {
            try {
                int x = Integer.parseInt(coords[0]);
                int y = Integer.parseInt(coords[1]);
                world.addObstacle(new Obstacle(obstacleType, x, y, x + obstacleSize, y + obstacleSize), true);
            } catch (NumberFormatException e) {
                System.out.println("Invalid obstacle format. Use: -o x,y (e.g., -o 4,5)");
            }
        } else {
            System.out.println("Invalid obstacle argument. Use: -o x,y");
        }
    }

    private void startAdminConsole() {
        new Thread(() -> {
            try (Scanner scanner = new Scanner(System.in)) {
                runAdminLoop(scanner);
            }
        }, "AdminConsole").start();
    }

    private void runAdminLoop(Scanner scanner) {
        // Check if we're running in an interactive environment (has console)
        if (System.console() == null) {
            System.out.println("Non-interactive environment detected. Admin console disabled.");
            return;
        }

        while (isRunning) {
            printAdminPrompt();
            String input = getSystemInput(scanner);
            if (input == null) continue;

            String[] parts = input.split("\\s+", 2);
            String command = parts[0];

            handleCommandFromAdmin(scanner, command, parts);
        }
        scanner.close();
    }

    private void handleCommandFromAdmin(Scanner scanner, String command, String[] parts) {
        switch (command) {
            case "quit":
                System.out.println("Shutting down server...");
                shutdown();
                break;
            case "robots":
                System.out.println(world.getAllRobotsInfo());
                break;
            case "dump":
                System.out.println(world.getFullWorldState());
                break;
            case "display":
                world.displayWorld();
                break;
            case "purge":
                executePurgeCommand(parts);
                break;
            case "save":
                executeSaveCommand(parts, scanner);
                break;
            case "restore":
                executeRestoreCommand(parts);
                break;
            default:
                System.out.println("Unknown admin command.");
        }
    }

    private String getSystemInput(Scanner scanner) {
        String input;

        try {
            input = scanner.nextLine().trim().toLowerCase();
        } catch (java.util.NoSuchElementException e) {
            sleepBriefly();
            return null;
        }

        if (input.isEmpty()) {
            return null;
        }
        return input;
    }

    private static void printAdminPrompt() {
        System.out.println("Valid Commands: 'quit', 'robots', 'dump', 'display', 'purge <robot name>', 'save <worldName>', 'restore <worldName>'");
        System.out.print("[Admin]: ");
    }

    private void sleepBriefly() {
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void executeRestoreCommand(String[] parts) {
        if (parts.length < 2 || parts[1].trim().isEmpty()) {
            System.out.println("Invalid command: Usage: restore <worldName>");
        } else {
            try {
                restoreWorld(parts[1].trim());
                System.out.println("World '" + parts[1] + "' restored successfully");
                world.displayWorld();
            } catch (SQLException e) {
                System.out.println("Failed to restore world: " + e.getMessage());
            }
        }
    }

    private void executeSaveCommand(String[] parts, Scanner scanner) {
        if (parts.length < 2 || parts[1].trim().isEmpty()) {
            System.out.println("Invalid command: Usage: save <worldName>");
        } else {
            try {
                String worldName = parts[1].trim();
                boolean worldExists = worldDAO.worldExists(worldName);
                boolean forceOverwrite = false;

                if (worldExists) {
                    System.out.print("World '" + worldName + "' already exists. Overwrite? (y/n): ");
                    String response = scanner.nextLine().trim().toLowerCase();
                    forceOverwrite = response.equals("y");
                    if (!forceOverwrite) {
                        System.out.println("Save cancelled.");
                        return;
                    }
                }

                worldDAO.saveWorld(worldName,world, forceOverwrite);
                System.out.println("World '" + worldName + "' has been saved successfully");
            } catch (SQLException e) {
                System.out.println("Failed to save world: " + e.getMessage());
            }
        }
    }

    private void executePurgeCommand(String[] parts) {
        if (parts.length < 2 || parts[1].trim().isEmpty()) {
            System.out.println("Error: Missing robot name");
        } else {
            String robotName = parts[1].trim();
            Response removeResponse = world.removeRobot(robotName);
            System.out.println(removeResponse.getMessage());
        }
    }

    public static void shutdown() {
        isRunning = false;
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
        } catch (IOException e) {
            System.out.println("Got an error when shutting down: " + e);
        }
    }

    public static boolean isServerInitialized() {
        return serverSocket != null && !serverSocket.isClosed();
    }

    private static class ArgumentParseResult {
        int portNumber;
        int worldSize;
        String obstacleArg;
        boolean testMode;

        ArgumentParseResult(ServerConfig serverConfig, String obstacleArg) {
            this.portNumber = serverConfig.portNumber;
            this.worldSize = serverConfig.worldSize;
            this.obstacleArg = obstacleArg;
            this.testMode = serverConfig.testMode;
        }
    }

    public static class ServerConfig {
        public int portNumber;
        public int worldSize;
        boolean testMode;

        public ServerConfig(int portNumber, int worldSize, boolean testMode) {
            this.portNumber = portNumber;
            this.worldSize = worldSize;
            this.testMode = testMode;
        }
    }
}