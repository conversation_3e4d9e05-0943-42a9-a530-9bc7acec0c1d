package za.co.wethinkcode.robots.server;

import org.json.JSONArray;
import org.json.JSONObject;
import java.util.*;
import java.util.Random;
import za.co.wethinkcode.robots.client.Position;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.commands.*;
import za.co.wethinkcode.robots.handlers.*;

public class World {
    private final CommandHandler commandHandler;
    private int width;
    private int height;
    private int halfWidth;
    private int halfHeight;
    private int maxShieldStrength;
    private int shieldRepairTime;
    private int reloadTime;
    private int visibility;
    private final List<Obstacle> obstacles = new ArrayList<>();
    private List<Robot> robots = new ArrayList<>();

    public World(int worldSize) {
        this(worldSize, worldSize);
    }

    public World(int width, int height) {
        setDimensions(width, height);
        this.visibility = this.halfWidth;
        this.commandHandler = new CommandHandler(this);
    }

    public void setDimensions(int width, int height) {
        // if the dimensions are even then plus one
        // otherwise if it's odd use the passed in value
        // this is to ensure that a 2x2 world acts the same as 3x3 world
        // so a 2x2 world would have the width and height as 3x3 world
        this.width = (width % 2 == 0) ? width + 1 : width;
        this.height = (height % 2 == 0) ? height + 1 : height;
        // note the max here is 0 so that in a 1x1 world movement is not possible
        // See https://curriculum.wethinkco.de/brownfields/02-build-pipelines/stories/#_at_the_edge_of_the_world
        this.halfWidth = Math.max(0, this.width / 2);
        this.halfHeight = Math.max(0, this.height / 2);
    }

    public void setDefaultDimensions() {
        setDimensions(100, 50);
    }

    public void setWorldProperties(int shieldRepairTime, int reloadTime, int maxShieldStrength, int visibility) {
        this.shieldRepairTime = shieldRepairTime;
        this.reloadTime = reloadTime;
        this.maxShieldStrength = maxShieldStrength;
        this.visibility = visibility;
    }

    public void setDefaultWorldProperties() {
        int visibility = (int) (this.getWidth() * 0.30);

        this.shieldRepairTime = 5;
        this.reloadTime = 3;
        this.maxShieldStrength = 10;
        this.visibility = visibility;
    }

    public void execute(Command command, CommandHandler.CompletionHandler completionHandler) {
        commandHandler.handle(command,completionHandler);
    }

    public void displayWorld() {
       System.out.println(displayViewport(-halfWidth, halfHeight, width, height));
    }

    public String displayViewport(int originX, int originY, int viewWidth, int viewHeight){
        String[][] grid = buildBaseGrid(originX, originY, viewWidth, viewHeight);
        addObstacles(grid, originX,originY);
        addRobots(grid, originX, originY);
        return gridToString(grid);
    }


    private String[][] buildBaseGrid(int originX, int originY, int viewWidth, int viewHeight) {
        // Create a 2D grid array of the specified viewport size
        String[][] grid = new String[viewHeight][viewWidth];

        for (int i = 0; i < viewHeight; i++) {
            for (int j = 0; j < viewWidth; j++) {
                int x = originX + j;
                int y = originY - i;

                // Mark in-bounds coordinates with ◾️, and out-of-bounds with empty space
                grid[i][j] = isWithinBounds(x, y) ? "◾️" : "  ";
            }
        }

        return grid;
    }

    private void addObstacles(String[][] grid, int originX, int originY) {
        for (Obstacle obs : obstacles) {
            fillObstacle(grid, obs, originX, originY);
        }
    }

    private void fillObstacle(String[][] grid, Obstacle obs, int originX, int originY) {
        for (int y = obs.getY(); y < obs.getMaxY(); y++) {
            for (int x = obs.getX(); x < obs.getMaxX(); x++) {
                if (!isInsideGrid(new Position(x, y), new Position(originX, originY), grid[0].length, grid.length)) {
                    continue;
                }
                int gx = x - originX;
                int gy = originY - y;
                grid[gy][gx] = obs.type().getSymbol();
            }
        }
    }

    private void addRobots(String[][] grid, int originX, int originY) {
        for (Robot robot : robots) {
            int x = robot.getX();
            int y = robot.getY();

            // Check if the robot is inside the visible viewport
            if (isInsideGrid(new Position(x, y), new Position(originX, originY), grid[0].length, grid.length)) {
                int gx = x - originX;
                int gy = originY - y;

                // Place the robot on the grid
                grid[gy][gx] = "🤖";
            }
        }
    }

    private boolean isInsideGrid(Position coordinatePosition, Position originPosition, int viewWidth, int viewHeight) {
        // Determines if a world coordinate (x, y) fits inside the visible viewport
        return coordinatePosition.getX() >= originPosition.getX() && coordinatePosition.getX() < originPosition.getX() + viewWidth && coordinatePosition.getY() <= originPosition.getY() && coordinatePosition.getY() > originPosition.getY() - viewHeight;
    }


    private String gridToString(String[][] grid) {
        // Converts the 2D grid array into a string
        StringBuilder sb = new StringBuilder();
        for (String[] row : grid) {
            for (String cell : row) {
                sb.append(cell).append(" ");
            }
            sb.append("\n");
        }
        return sb.toString();
    }

    public String displayDirectionalCross(Robot robot, int maxDistance) {
        int robotX = robot.getX();
        int robotY = robot.getY();

        // Calculate the actual limits based on world bounds and maxDistance
        int minX = Math.max(robotX - maxDistance, -halfWidth);
        int maxX = Math.min(robotX + maxDistance, halfWidth);
        int minY = Math.max(robotY - maxDistance, -halfHeight);
        int maxY = Math.min(robotY + maxDistance, halfHeight);

        String[][] grid = createEmptyGrid(minX, maxX, minY, maxY);

        // Fill cross lines with base tile ◾️
        drawDirectionalCross(grid, robot, minX, maxY);

        // Place obstacles on cross lines within bounds
        markObstacles(grid, robot, minX, maxY);

        // Place other robots on cross lines within bounds
        markRobots(grid, robot, minX, maxY);

        return gridToString(grid);
    }

    private String[][] createEmptyGrid(int minX, int maxX, int minY, int maxY) {
        int width = maxX - minX + 1;
        int height = maxY - minY + 1;
        String[][] grid = new String[height][width];

        for (String[] row : grid) {
            Arrays.fill(row, "  ");
        }

        return grid;
    }

    private void drawDirectionalCross(String[][] grid, Robot robot, int minX, int maxY) {
        int robotX = robot.getX();
        int robotY = robot.getY();

        int col = robotX - minX;
        int row = maxY - robotY;

        // Vertical line (robotX column)
        for (int i = 0; i < grid.length; i++) {
            grid[i][col] = "◾️";
        }

        // Horizontal line (robotY row)
        for (int j = 0; j < grid[0].length; j++) {
            grid[row][j] = "◾️";
        }

        // Place current robot
        grid[row][col] = "🤖";
    }

    private void markObstacles(String[][] grid, Robot robot, int minX, int maxY) {
        int robotX = robot.getX();
        int robotY = robot.getY();

        // Only mark obstacles that lie on same X or Y as the robot
        for (Obstacle obs : obstacles) {
            fillMarkedObstacles(obs, robot, new Position(minX, maxY), grid);
        }
    }

    private void fillMarkedObstacles(Obstacle obs, Robot robot, Position position, String[][] grid) {
        int robotX = robot.getX();
        int robotY = robot.getY();

        int maxY = position.getY();
        int minX = position.getX();

        for (int y = obs.getY(); y < obs.getMaxY(); y++) {
            for (int x = obs.getX(); x < obs.getMaxX(); x++) {
                if (x != robotX && y != robotY) continue;

                int row = maxY - y;
                int col = x - minX;

                if (!isWithinGrid(row, col, grid)) continue;

                grid[row][col] = obs.type().getSymbol();
            }
        }
    }

    private void markRobots(String[][] grid, Robot robot, int minX, int maxY) {
        int robotX = robot.getX();
        int robotY = robot.getY();

        for (Robot r : robots) {
            if (r.equals(robot)) continue;

            int x = r.getX();
            int y = r.getY();

            if (x != robotX && y != robotY) continue;

            int row = maxY - y;
            int col = x - minX;

            if (isWithinGrid(row, col, grid)) {
                grid[row][col] = "🤖";
            }
        }
    }

    private boolean isWithinGrid(int row, int col, String[][] grid) {
        return row >= 0 && row < grid.length && col >= 0 && col < grid[0].length;
    }


public Status isPositionValid(Position position) {
    return isPositionValid(position, null);
}

public Status isPositionValid(Position position, Robot currentRobot) {
    if (!isWithinBounds(position.getX(), position.getY())) {
        return Status.OutOfBounds;
    }

    Status obstacleStatus = checkObstacleCollision(position);
    if (obstacleStatus != null) {
        return obstacleStatus;
    }

    if (isRobotCollision(position, currentRobot)) {
        return Status.HitRobot;
    }

    return Status.OK;
}

private Status checkObstacleCollision(Position position) {
    // Check for obstacle collisions
    for (Obstacle obstacle : obstacles) {
        if (obstacle.contains(position)) {
            if (obstacle.type() == ObstacleType.PIT) {
                return Status.HitObstaclePIT;
            } else {
                return Status.HitObstacle;
            }
        }
    }
    return null;
}

private boolean isRobotCollision(Position position, Robot currentRobot) {
    // Check for robot collisions - exclude the current robot
    for (Robot existingRobot : robots) {
        // Skip checking collision with itself
        if (currentRobot != null && existingRobot == currentRobot) {
            continue;
        }
        if (existingRobot.getX() == position.getX() && existingRobot.getY() == position.getY()) {
            return true;
        }
    }
    return false;
}

    public boolean addObstacle(Obstacle obstacle) {
        return addObstacle(obstacle, false);
    }

    public boolean addObstacle(Obstacle obstacle, boolean ignoreSafetyChecks) {
        boolean overlaps = false;

        for (Obstacle existing : obstacles) {
                if (existing.overlaps(obstacle)) {
                overlaps = true;
                break;
            }
        }

        boolean withinBounds = ignoreSafetyChecks || isWithinBounds(obstacle.getMaxX(), obstacle.getMaxY());

        if (!overlaps && withinBounds) {
            obstacles.add(obstacle);
            return true;
        }

        return false;
    }

public Status addRobot(Robot robot) {
    // Check for duplicate robot names first
    for (Robot nextRobot : robots) {
        if (nextRobot.getName().equals(robot.getName())) {
            return Status.ExistingName;
        }
    }

    // Generate all possible positions systematically
    List<Position> allPositions = getPositions();

    // Shuffle the list to ensure random placement while being exhaustive
    Collections.shuffle(allPositions);

    // For the first robot, try (0,0) first if available, otherwise use shuffled list
    if (robots.isEmpty()) {
        Position centerPos = new Position(0, 0);
        Status status = isPositionValid(centerPos);
        if (status == Status.OK) {
            robot.setPosition(0, 0);
            this.robots.add(robot);
            return Status.OK;
        }
    }

    // Try all positions systematically
    for (Position pos : allPositions) {
        Status status = isPositionValid(pos);
        if (status == Status.OK) {
            robot.setPosition(pos.getX(), pos.getY());
            this.robots.add(robot);
            return Status.OK;
        }
    }

    // If no valid position found after trying all positions, world is full
    return Status.WorldFull;
}

    private List<Position> getPositions() {
        List<Position> allPositions = new ArrayList<>();
        for (int x = -halfWidth; x <= halfWidth; x++) {
            for (int y = -halfHeight; y <= halfHeight; y++) {
                allPositions.add(new Position(x, y));
            }
        }
        return allPositions;
    }

    public Robot findRobot(String name) {
        for (Robot robot : robots) {
            if (robot.getName().equals(name)) {
                return robot;
            }
        }
        return null;
    }

    public Response removeRobot(String robotName) {
        Robot robot = findRobot(robotName);
        if (robot == null) {
            return new Response("ERROR", "Robot not found.");
        }

        robots.remove(robot);
        return new Response("OK", "Removed robot " + robotName + " from the world.");
    }

    public void removeAllRobots(){
        robots = new ArrayList<>();
    }

    public void stateForRobot(Robot robot, Response response) {
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("position", new JSONArray().put(robot.getX()).put(robot.getY()));
        jsonObject.put("direction", robot.orientation().toUpperCase());
        jsonObject.put("shields", robot.getShields());
        jsonObject.put("shots", robot.getShots());
        jsonObject.put("status", robot.status.toString().toUpperCase());
        response.object.put("state", jsonObject);
    }

    public String getAllRobotsInfo() {
        if (robots.isEmpty()) {
            return "No robots in the world.";
        }
        StringBuilder sb = new StringBuilder("Robots in the world:");
        for (Robot robot : robots) {
            Response response = new Response("", "State for " + robot.getName());

            stateForRobot(robot, response);

            sb.append("\n- ").append(robot.getName()).append(" ").append(response.toJSONString());
        }
        return sb.toString();
    }

    public String getFullWorldState() {
        StringBuilder sb = new StringBuilder("World State:\n");
        sb.append("Dimensions: ").append(width).append(" x ").append(height).append("\n");
        sb.append("Obstacles (").append(obstacles.size()).append("):\n");
        for (Obstacle obs: obstacles) {
            sb.append(" - \n").append(obs.toString());
        }
        sb.append("- \n Robots (").append(robots.size()).append("):\n");
        for (Robot robot : robots) {
            sb.append("- ").append(robot.getName())
                    .append(" at (").append(robot.getX()).append(", ").append(robot.getY()).append(")\n");
        }
        return sb.toString();
    }

    public List<Robot> getRobots() {
        return robots;
    }

    public List<Obstacle> getObstacles() {
        return obstacles;
    }

    public int getHalfWidth() {
        return halfWidth;
    }

    public int getHalfHeight() {
        return halfHeight;
    }

    public int getMaxShieldStrength() {
        return maxShieldStrength;
    }

    public int getReloadTime() {
        return reloadTime;
    }

    public int getShieldRepairTime() {
        return shieldRepairTime;
    }

    public int getVisibility() {
        return visibility;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public void generateDefaultObstacles() {
        int obstacleCount = (int) ((height + width) * 0.30);

        for (int i = 0; i <= obstacleCount; i++) {
            Random random = new Random();
            boolean added = false;

            while (!added) {
                int randomWidth = random.nextInt(1, 4);
                int randomHeight = random.nextInt(1, 4);
                int randomX = random.nextInt(-halfWidth, halfWidth);
                int randomY = random.nextInt(-halfHeight, halfHeight);

                ObstacleType type = ObstacleType.values()[random.nextInt(ObstacleType.values().length)];
                Obstacle obstacle = new Obstacle(type, randomX, randomY, randomWidth, randomHeight);

                if (addObstacle(obstacle)) {
                    added = true;
                }
            }
        }
    }

    private boolean isWithinBounds(int x, int y) {
        return x >= -halfWidth && x <= halfWidth  && y >= -halfHeight && y <= halfHeight;
    }
}