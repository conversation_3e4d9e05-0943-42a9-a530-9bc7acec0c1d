#!/usr/bin/env python3
"""
TODO: Implement cat_fact_client.py (Section 2)

Responsibilities:
- Demonstrate Python HTTP client using http.client
- Send GET request to catfact.ninja/fact
- Print HTTP status code and response body
- Handle errors gracefully

Requirements:
- Use built-in http.client.HTTPSConnection
- Include Accept: application/json header
- Print both status code and decoded JSON response
- Include proper error handling
"""

# TODO: Implementation needed

def fetch_cat_fact():
    """Fetch a random cat fact from catfact.ninja API"""
    # TODO: Implement using http.client.HTTPSConnection
    pass

if __name__ == "__main__":
    fetch_cat_fact()
