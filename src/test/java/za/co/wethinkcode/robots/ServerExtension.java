package za.co.wethinkcode.robots;

import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import java.io.IOException;
import java.io.PrintWriter; // Added import
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

public class ServerExtension implements BeforeEachCallback, AfterEachCallback {
    private Process serverProcess;
    private Thread serverThread;
    private Boolean usingOwnServer = false;
    private Boolean debug = false;
    private Boolean useDocker = false;
    private String dockerContainerName = null;
    private static int serverPort = 5000; // Default port

    @Override
    public void beforeEach(ExtensionContext context) throws Exception {
        ServerConfig config = context.getRequiredTestMethod().getAnnotation(ServerConfig.class);
        String jarPath = System.getenv("SERVER_JAR_PATH");
        String useOwnServer = System.getenv("USE_OWN_SERVER");
        String debugMode = System.getenv("DEBUG");
        String dockerMode = System.getenv("USE_DOCKER");
        String serverPortEnv = System.getenv("SERVER_PORT");

        boolean isDockerTestMode = isDockerTestMode(useOwnServer, serverPortEnv);

        if (isDockerTestMode) {
            handleDockerTestMode(config, serverPortEnv);
            return;
        }

        setServerPortFromEnv(serverPortEnv);

        if (isUsingOwnServer(useOwnServer)) {
            usingOwnServer = true;
            handleOwnServer(config, serverPortEnv);
            return;
        }

        setDebugMode(debugMode);
        setDockerMode(dockerMode);

        String jarServerPath = findRobotWorldsJar("libs");
        if (isRunningFromIntellij() && jarServerPath != null && jarPath == null) {
            jarPath = String.format("./libs/%s", jarServerPath);
        }
        if (useDocker) {
            startDockerServer(config);
        } else {
            startJarServer(config, jarPath);
        }

        waitForServerOrThrow(config);
    }

    private boolean isDockerTestMode(String useOwnServer, String serverPortEnv) {
        return "false".equalsIgnoreCase(useOwnServer) && serverPortEnv != null;
    }

    private void setServerPortFromEnv(String serverPortEnv) {
        if (serverPortEnv != null && !serverPortEnv.isEmpty()) {
            try {
                serverPort = Integer.parseInt(serverPortEnv);
            } catch (NumberFormatException e) {
                System.err.println("Invalid SERVER_PORT value: " + serverPortEnv + ", using default port 5000");
                serverPort = 5000;
            }
        }
    }

    private boolean isUsingOwnServer(String useOwnServer) {
        return "true".equalsIgnoreCase(useOwnServer);
    }

    private void handleOwnServer(ServerConfig config, String serverPortEnv) {
        String[] args = config != null ? config.arguments() : new String[0];
        int port = serverPortEnv != null ? serverPort : extractPort(args);
        boolean ready = waitForServer("localhost", port);

        if (!ready) {
            throw new RuntimeException("External server is not ready on port " + port + " within timeout.");
        }
    }

    private void setDebugMode(String debugMode) {
        if ("true".equalsIgnoreCase(debugMode)) {
            debug = true;
        }
    }

    private void setDockerMode(String dockerMode) {
        if ("true".equalsIgnoreCase(dockerMode)) {
            useDocker = true;
        }
    }

    private void waitForServerOrThrow(ServerConfig config) {
        String[] args = config != null ? config.arguments() : new String[0];
        int port = extractPort(args);
        boolean ready = waitForServer("localhost", port);

        if (!ready) {
            throw new RuntimeException("Server failed to start on port " + port + " within timeout.");
        }
    }

    private void handleDockerTestMode(ServerConfig config, String serverPortEnv) throws Exception {
        usingOwnServer = true; // We're using an existing server
        try {
            serverPort = Integer.parseInt(serverPortEnv);
        } catch (NumberFormatException e) {
            System.err.println("Invalid SERVER_PORT value: " + serverPortEnv + ", using default port 5050");
            serverPort = 5050;
        }

        // For Docker test mode, we need to restart the container with the specific test configuration
        if (config != null && config.arguments().length > 0) {
            System.out.println("Restarting Docker container with test-specific config: " + String.join(" ", config.arguments()));
            restartDockerWithConfig(config);
        }

        // Wait for the Docker server to be ready
        boolean ready = waitForServer("localhost", serverPort);

        if (!ready) {
            throw new RuntimeException("Docker server is not ready on port " + serverPort + " within timeout.");
        }
    }

    private void restartDockerWithConfig(ServerConfig config) throws IOException, InterruptedException {
        // Stop the existing container
        ProcessBuilder stopPb = new ProcessBuilder("docker", "stop", "robot-test-server");
        Process stopProcess = stopPb.start();
        stopProcess.waitFor();

        // Wait a moment for the container to fully stop
        Thread.sleep(2000);

        // Start new container with specific config
        List<String> dockerCommand = new ArrayList<>();
        dockerCommand.add("docker");
        dockerCommand.add("run");
        dockerCommand.add("-d");
        dockerCommand.add("--rm");
        dockerCommand.add("-p");
        dockerCommand.add(serverPort + ":" + serverPort);
        dockerCommand.add("--name");
        dockerCommand.add("robot-test-server");
        dockerCommand.add("robot-worlds-server:0.3.0"); // Updated to correct version
        dockerCommand.add("-p");
        dockerCommand.add(String.valueOf(serverPort));

        // Add the specific test configuration
        dockerCommand.addAll(Arrays.asList(config.arguments()));

        System.out.println("Starting Docker container with command: " + String.join(" ", dockerCommand));

        ProcessBuilder pb = new ProcessBuilder(dockerCommand);
        pb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
        pb.redirectError(ProcessBuilder.Redirect.INHERIT);

        Process dockerProcess = pb.start();
        int exitCode = dockerProcess.waitFor();

        if (exitCode != 0) {
            throw new RuntimeException("Failed to start Docker container with specific config. Exit code: " + exitCode);
        }

        // Wait for the server to be ready
        Thread.sleep(5000);
    }

    private void startJarServer(ServerConfig config, String jarPath) {
        List<String> command = new ArrayList<>();
        buildCommand(config, jarPath, command);

        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
        pb.redirectError(ProcessBuilder.Redirect.INHERIT);

        serverThread = new Thread(() -> {
            try {
                serverProcess = pb.start();
                serverProcess.waitFor();
            } catch (IOException | InterruptedException e) {
                throw new RuntimeException("Failed to start or monitor server process", e);
            }
        });

        serverThread.start();
    }

    private void startDockerServer(ServerConfig config) throws IOException, InterruptedException {
        int port = findAvailablePort();
        serverPort = port;

        dockerContainerName = "robot-test-" + System.currentTimeMillis();

        List<String> dockerCommand = new ArrayList<>();
        dockerCommand.add("docker");
        dockerCommand.add("run");
        dockerCommand.add("-d");
        dockerCommand.add("-p");
        dockerCommand.add(port + ":" + port);
        dockerCommand.add("--name");
        dockerCommand.add(dockerContainerName);
        dockerCommand.add("robot-worlds-server:0.3.0");
        dockerCommand.add("-p");
        dockerCommand.add(String.valueOf(port));

        if (config != null && config.arguments().length > 0) {
            List<String> filteredArgs = new ArrayList<>();
            for (int i = 0; i < config.arguments().length; i++) {
                if ("-p".equals(config.arguments()[i])) {
                    i++;
                } else {
                    filteredArgs.add(config.arguments()[i]);
                }
            }
            dockerCommand.addAll(filteredArgs);
            System.out.println("Starting Docker server with custom args: " + Arrays.toString(config.arguments()) + " on port " + port);
        } else {
            System.out.println("Starting Docker server with default configuration on port " + port);
        }

        ProcessBuilder pb = new ProcessBuilder(dockerCommand);
        pb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
        pb.redirectError(ProcessBuilder.Redirect.INHERIT);

        Process dockerProcess = pb.start();
        int exitCode = dockerProcess.waitFor();

        if (exitCode != 0) {
            throw new RuntimeException("Failed to start Docker container. Exit code: " + exitCode);
        }

        Thread.sleep(5000);
    }

    private int findAvailablePort() throws IOException {
        for (int port = 5051; port <= 6000; port++) {
            try (ServerSocket socket = new ServerSocket(port)) {
                return port;
            } catch (IOException e) {
                // Port in use
            }
        }
        throw new IOException("No available ports found in range 5051-6000");
    }

    private void buildCommand(ServerConfig config, String jarPath, List<String> command) {
        if (debug) {
            buildMavenCommand(config, command);
        } else {
            buildJarCommand(config, jarPath, command);
        }
    }

    private void buildMavenCommand(ServerConfig config, List<String> command) {
        command.add("mvn");
        command.add("exec:java");
        command.add("-Dexec.mainClass=za.co.wethinkcode.robots.server.Server");

        String execArgs = getExecArgs(config);
        command.add("-Dexec.args=" + execArgs);
    }

    private String getExecArgs(ServerConfig config) {
        String execArgs = "";
        if (config != null && config.arguments().length > 0) {
            execArgs = String.join(" ", config.arguments());
            System.out.println("Starting server with custom args: " + execArgs);
        } else {
            execArgs = "-s 1";
            System.out.println("Starting server with default configuration.");
        }
        return execArgs;
    }

    private void buildJarCommand(ServerConfig config, String jarPath, List<String> command) {
        command.add("java");
        command.add("-jar");
        command.add(jarPath);

        if (config != null && config.arguments().length > 0) {
            command.addAll(Arrays.asList(config.arguments()));
            System.out.println("Starting server with custom args: " + command.subList(3, command.size()));
        } else {
            System.out.println("Starting server with default configuration.");
        }
    }

    @Override
    public void afterEach(ExtensionContext context) throws Exception {
        Thread.sleep(1000); // Ensure test completes

        if (useDocker && dockerContainerName != null) {
            stopDockerServer();
        } else if (!usingOwnServer && serverProcess != null) {
            stopJarServer();
        }

        if (serverThread != null && serverThread.isAlive()) {
            serverThread.interrupt();
            serverThread.join(2000);
        }

        useDocker = false;
        usingOwnServer = false;
        dockerContainerName = null;
        serverProcess = null;
        serverThread = null;
    }

    private void stopJarServer() throws InterruptedException {
        if (serverProcess == null) return;

        // Send shutdown command to server
        try (Socket socket = new Socket("localhost", serverPort);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true)) {
            out.println("{\"robot\": \"SYSTEM\", \"command\": \"shutdown\", \"arguments\": []}");
            Thread.sleep(500); // Give server time to process shutdown
        } catch (IOException e) {
            System.err.println("Failed to send shutdown command: " + e.getMessage());
        }

        serverProcess.destroy();
        boolean terminated = serverProcess.waitFor(3, TimeUnit.SECONDS);

        if (!terminated) {
            serverProcess.destroyForcibly();
            serverProcess.waitFor(2, TimeUnit.SECONDS);
        }

        Thread.sleep(3000); // Ensure port is released
    }

    private void stopDockerServer() throws InterruptedException, IOException {
        if (dockerContainerName == null) return;

        System.out.println("Stopping Docker container: " + dockerContainerName);

        ProcessBuilder stopPb = new ProcessBuilder("docker", "stop", "-t", "5", dockerContainerName);
        stopPb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
        stopPb.redirectError(ProcessBuilder.Redirect.INHERIT);
        Process stopProcess = stopPb.start();
        int stopExitCode = stopProcess.waitFor();

        if (stopExitCode != 0) {
            System.err.println("Warning: docker stop returned exit code " + stopExitCode);
            ProcessBuilder killPb = new ProcessBuilder("docker", "kill", dockerContainerName);
            Process killProcess = killPb.start();
            killProcess.waitFor(5, TimeUnit.SECONDS);
        }

        ProcessBuilder rmPb = new ProcessBuilder("docker", "rm", "-f", dockerContainerName);
        rmPb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
        rmPb.redirectError(ProcessBuilder.Redirect.INHERIT);
        Process rmProcess = rmPb.start();
        int rmExitCode = rmProcess.waitFor();

        if (rmExitCode == 0) {
            System.out.println("Docker container cleaned up: " + dockerContainerName);
        } else {
            System.err.println("Warning: docker rm returned exit code " + rmExitCode);
        }

        Thread.sleep(3000);
    }

    private int extractPort(String[] args) {
        for (int i = 0; i < args.length - 1; i++) {
            if ("-p".equals(args[i])) {
                try {
                    return Integer.parseInt(args[i + 1]);
                } catch (NumberFormatException e) {
                    System.err.println("Invalid port number after -p: " + args[i + 1]);
                    break;
                }
            }
        }
        return 5000;
    }

    private boolean waitForServer(String host, int port) {
        long start = System.currentTimeMillis();
        int maxRetries = 25;

        while (System.currentTimeMillis() - start < 10000 && maxRetries > 0) {
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(host, port), 1500);
                Thread.sleep(700);
                try (Socket verifySocket = new Socket()) {
                    verifySocket.connect(new InetSocketAddress(host, port), 1000);
                    return true;
                }
            } catch (IOException | InterruptedException ignored) {
                maxRetries--;
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        return false;
    }

    private static boolean isRunningFromIntellij() {
        String command = System.getProperty("sun.java.command");

        boolean result = false;

        if (command != null) {
           result = command.toLowerCase().contains("com.intellij");
        }

        return result;
    }

    private static String findRobotWorldsJar(String libsPath) {
        Path libsDir = Paths.get(libsPath);

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(libsDir)) {
            for (Path entry : stream) {
                String fileName = entry.getFileName().toString();
                if (fileName.startsWith("robot-worlds-server") && fileName.endsWith(".jar")) {
                    return fileName;
                }
            }
        } catch (IOException e) {
            System.err.println("Error reading libs directory: " + e.getMessage());
        }

        return null;
    }

    public static int getServerPort() {
        return serverPort;
    }
}