package za.co.wethinkcode.robots.acceptanceTest;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import za.co.wethinkcode.robots.ServerConfig;
import za.co.wethinkcode.robots.ServerExtension;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;
import za.co.wethinkcode.robots.server.World;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player,
 * I want to look around and see other robots in my view
 * So that I can navigate strategically or engage them in battle
 */
@ExtendWith(ServerExtension.class)
class RobotLookTests {

    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();


    @BeforeEach
    void connect() throws IOException {
        int port = ServerExtension.getServerPort();
        serverClient.connect(DEFAULT_IP, port);
    }

    @AfterEach
    void disconnect() {
        serverClient.disconnect();
    }

    @Test
    void robotShouldBeAbleToSee() {
        // Given a robot is launched into the world
        String launchFBI = "{" +
                "  \"robot\": \"FBI\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseFBI = serverClient.sendRequest(launchFBI);
        assertEquals("OK", responseFBI.get("result").asText());

        // When "FBI" performs the look command
        String lookFBI = "{" +
                "\"robot\": \"FBI\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";


        JsonNode lookResponse = serverClient.sendRequest(lookFBI);
        assertEquals("OK", lookResponse.get("result").asText());
        int visibility = lookResponse.get("data").get("visibility").asInt();
        assertNotEquals(0, visibility, "The robot should have a positive visibility or it won't be able to see ");

    }

    @Test
    @ServerConfig(arguments = {"-s", "2", "-o", "0,1"})
    void detectNearbyRobotsAndObstacle() {
        // Given a 2x2 world (from -s 2) with obstacle at 0,1
        // Launch Alpha robot at a valid position in 2x2 world
        String launchRequest = "{" +
                "\"robot\": \"Alpha\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"tank\", \"1\", \"1\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(launchRequest);
        assertEquals("OK", response.get("result").asText(), "Robot Alpha failed to launch.");

        // When Alpha sends the look command
        String lookCommand = "{" +
                "\"robot\": \"Alpha\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode lookResponse = serverClient.sendRequest(lookCommand);
        assertEquals("OK", lookResponse.get("result").asText(), "Look command failed.");

        // Then the response should include visibility and objects data
        JsonNode data = lookResponse.get("data");
        assertNotNull(data, "Data missing from look response.");
        assertTrue(data.has("visibility"), "Visibility missing from look response.");
        assertTrue(data.has("objects"), "Objects array missing from look response.");
    }

    @Test
    @ServerConfig(arguments = {"-s", "2", "-o", "0,1"})
    void detectObstacleInLineOfSight() {
        // Given a 2x2 world with an obstacle at [0,1]
        // And the robot is at [0,0] facing North
        String launch = "{" +
                "\"robot\": \"Alpha\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"tank\", \"2\", \"2\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launch);
        assertEquals("OK", launchResponse.get("result").asText());

        // When I issue the look command
        String look = "{" +
                "\"robot\": \"Alpha\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode lookResponse = serverClient.sendRequest(look);
        assertEquals("OK", lookResponse.get("result").asText());

        // Then the response includes: → OBSTACLE at distance 1
        JsonNode objects = lookResponse.get("data").get("objects");
        assertNotNull(objects);
        boolean obstacleFound = false;

        for (JsonNode obj : objects) {
            if (obj.get("type").asText().equalsIgnoreCase("OBSTACLE") &&
                    obj.get("distance").asInt() == 1) {
                obstacleFound = true;
                break;
            }
        }

        assertTrue(obstacleFound, "Expected an OBSTACLE at distance 1 in the robot's facing direction.");
    }

}
